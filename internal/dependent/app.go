package dependent

import (
	"fmt"
	"golem-core/pkg/config"
	"golem-core/pkg/storage/cache/redis"
	"golem-core/pkg/storage/database/mongodb"
	"golem-core/pkg/storage/queue/rabbitmq"
	"log"
)

// App 应用容器，管理所有依赖
type App struct {
	// 配置
	Config *config.Config

	// 存储层
	MongoDB  *mongodb.Client
	Redis    *redis.Client
	RabbitMQ *rabbitmq.Client
}

// NewApp 创建应用实例
func NewApp(cfg *config.Config) *App {
	app := &App{
		Config: cfg,
	}

	// 初始化存储层
	err := app.initStorage()
	if err != nil {
		log.Printf("error init storage: %v", err)
	}

	log.Printf("app dependency init success")
	return app
}

// initStorage 初始化存储层
func (app *App) initStorage() error {
	// connect mongodb
	mongodbClient, err := mongodb.NewClient(&app.Config.Database)
	if err != nil {
		return fmt.Errorf("error connect mongodb: %v", err)
	}
	app.MongoDB = mongodbClient

	// connect redis
	redisClient, err := redis.NewClient(&app.Config.Redis)
	if err != nil {
		return fmt.Errorf("error connect redis: %v", err)
	}
	app.Redis = redisClient

	// connect rabbitmq
	rabbitmqClient, err := rabbitmq.NewClient(&app.Config.Rabbitmq)
	if err != nil {
		return fmt.Errorf("error connect rabbitmq: %v", err)
	}
	app.RabbitMQ = rabbitmqClient
	return nil
}

// Close 关闭应用，清理资源
func (app *App) Close() error {
	return nil
}
