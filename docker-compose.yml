services:
  # Golem Manage 应用
  golem-manage:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: golem-manage
    ports:
      - "10013:10013"
      - "10014:10014"
    environment:
      - GIN_MODE=release
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
    depends_on:
      - mongodb
      - redis
      - rabbitmq
    networks:
      - golem-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:10013/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB 数据库
  mongodb:
    image: mongo:7.0
    container_name: golem-mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123
      MONGO_INITDB_DATABASE: golem_core
    volumes:
      - mongodb_data:/data/db
    networks:
      - golem-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis 缓存
  redis:
    image: redis:7.2-alpine
    container_name: golem-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data:/data
    networks:
      - golem-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ 消息队列
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: golem-rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: golem
      RABBITMQ_DEFAULT_PASS: golem123
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - golem-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

# 网络配置
networks:
  golem-network:
    driver: bridge

# 数据卷配置
volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
