package config

import (
	"fmt"
	"github.com/spf13/viper"
	"strings"
)

// Config 通用配置
type Config struct {
	Application Application `mapstructure:"application"`
	Database    Database    `mapstructure:"database"`
	Redis       Redis       `mapstructure:"redis"`
	Rabbitmq    Rabbitmq    `mapstructure:"rabbitmq"`
}

// Application 应用配置
type Application struct {
	Mode string `mapstructure:"mode"`
	Auth struct {
		JWTSecret string `mapstructure:"jwt_secret"`
		JWTExpire int    `mapstructure:"jwt_expire"`
	} `mapstructure:"auth"`
	Manage Manage `mapstructure:"manage"`
	Agent  Agent  `mapstructure:"agent"`
}

// Manage 管理平台配置
type Manage struct {
	Name   string `mapstructure:"name"`
	Server struct {
		HttpListenPort int `mapstructure:"http_listen_port"`
		GrpcListenPort int `mapstructure:"grpc_listen_port"`
	} `mapstructure:"server"`
}

// Agent 配置
type Agent struct {
}

// Database 数据库配置
type Database struct {
	Driver   string `mapstructure:"driver"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"database"`
	MaxIdle  int    `mapstructure:"max_idle"`
	MaxOpen  int    `mapstructure:"max_open"`
}

// Redis 缓存配置
type Redis struct {
	Enable   bool   `mapstructure:"enable"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	Db       int    `mapstructure:"db"`
}

// Rabbitmq 消息队列配置
type Rabbitmq struct {
	Enable   bool   `mapstructure:"enable"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Vhost    string `mapstructure:"vhost"`
}

// LoadConfig 加载配置
func LoadConfig(configFile string) (*Config, error) {
	// set config file
	viper.SetConfigFile(configFile)
	// set config type
	viper.SetConfigType("yaml")
	// auto matic env
	viper.AutomaticEnv()
	// replace . with _
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// read config
	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config: %w", err)
	}

	// unmarshal config
	var cfg Config
	if err := viper.Unmarshal(&cfg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &cfg, nil
}
