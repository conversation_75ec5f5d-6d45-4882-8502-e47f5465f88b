version: 1.0.5

# Golem Core 应用配置文件
application:
  mode: "release"  # debug, release
  auth:
    jwt_secret: "golem_secret@jfghd4"
    jwt_expire: 86400  # 24小时，单位：秒
  manage:
    name: "Golem Manage"
    server:
      http_listen_port: 10013
      grpc_listen_port: 10014
  mapping:
    name: "Golem Mapping"
    server:
      http_listen_port: 10015
      grpc_listen_port: 10016
  agent:
    name: "Golem Agent"

# 数据库配置
database:
  driver: "mongodb"   # (default:mongodb, mongodb/mysql/postgres)
  host: "***************"
  port: 27017
  username: "admin"
  password: "golem5011"
  database: "GOLEM_MANAGE_DEV"
  max_idle: 10
  max_open: 100

# Redis 缓存配置
redis:
  enable: true    # (default:true, if false, use memory)
  host: "***************"
  port: 6379
  password: "golem5011"
  db: 1

# RabbitMQ 消息队列配置
rabbitmq:
  enable: false
  host: ***************
  port: 5672
  username: "admin"
  password: "golem5011"
  vhost: "Golem"