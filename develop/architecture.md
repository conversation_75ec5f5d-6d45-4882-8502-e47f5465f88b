# Golem 网络攻击面监测系统技术架构

## 1. 系统概述

Golem 是一个分布式网络攻击面监测系统，旨在实时发现、分析和监控网络资产的安全状态。

## 2. 核心架构

### 2.1 微服务架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Web Dashboard                            │
├─────────────────────────────────────────────────────────────┤
│                    API Gateway                              │
├─────────────────────────────────────────────────────────────┤
│  管理服务    │  扫描引擎    │  数据分析    │  告警服务         │
│  (Manage)   │   (Agent)   │  (Analysis)  │  (Alert)         │
├─────────────────────────────────────────────────────────────┤
│                    Event Bus (RabbitMQ)                     │
├─────────────────────────────────────────────────────────────┤
│  MongoDB     │  Redis      │  InfluxDB    │  Elasticsearch  │
│  (主数据)    │  (缓存)     │  (时序数据)   │  (日志搜索)      │
└─────────────────────────────────────────────────────────────┘
```